import { FindOptions, Op, Transaction } from "sequelize";
import { StaticDomainPoolDBInstance } from "../models/staticDomainPool";
import { lazy } from "@skywind-group/sw-utils";
import * as Errors from "../errors";
import {
    StaticDomainPoolService,
    ExtendedStaticDomain,
    StaticDomainPoolAttributes,
    StaticDomainPoolItemAttributes
} from "../entities/domainPool";
import { sequelize as db } from "../storage/db";
import { Models } from "../models/models";
import * as StaticDomainPoolCache from "../cache/staticDomainPoolCache";

const StaticDomainPoolItemModel = Models.StaticDomainPoolItemModel;
const StaticDomainPoolModel = Models.StaticDomainPoolModel;
const StaticDomainModel = Models.StaticDomainModel;

export const domainPoolAssociations: FindOptions<any>["include"] = [
    {
        model: StaticDomainModel,
        as: "domains",
        through: {
            attributes: ["isActive"]
        }
    }
];

function toInfo(domainPool: StaticDomainPoolDBInstance): StaticDomainPoolAttributes {

    const { domains, ...result } = domainPool.toJSON();

    return {
        ...result,
        domains: domains?.map(({ StaticDomainPoolItem, ...domain }) => {
            if (StaticDomainPoolItem) {
                domain.isActive = StaticDomainPoolItem.isActive;
            }
            return domain;
        }) || []
    };
}

function validateDomainsExist<T>(domainIds: number[], foundDomainIds: number[]) {
    const foundIds = new Set(foundDomainIds);
    const missingIds = domainIds.filter(id => !foundIds.has(id));
    if (missingIds.length > 0) {
        throw new Errors.DomainNotFoundError(missingIds[0]);
    }
}

async function validateStaticDomainsExist(domains: ExtendedStaticDomain[], transaction: Transaction) {
    const domainIds = domains.map(({ id }) => id);
    validateDomainsExist(domainIds, (await StaticDomainModel.findAll({
        where: { id: { [Op.in]: domainIds } },
        transaction
    })).map<number>(item => item.get("id")));
}



class StaticDomainPoolServiceImpl implements StaticDomainPoolService {

    public async create(data: StaticDomainPoolAttributes): Promise<StaticDomainPoolAttributes> {
        return db.transaction(async transaction => {
            const existing = await StaticDomainPoolModel.findOne(
                {
                    where: {
                        name: data.name
                    },
                    transaction
                }
            );

            if (existing) {
                throw new Errors.DomainPoolAlreadyExistsError();
            }

            const created = await StaticDomainPoolModel.create({ name: data.name }, { transaction });
            const staticDomainPoolId = created.get("id") as number;

            if (data.domains && data.domains.length > 0) {
                await validateStaticDomainsExist(data.domains, transaction);
                await StaticDomainPoolItemModel.bulkCreate(data.domains.map(domain => ({
                    staticDomainPoolId,
                    staticDomainId: domain.id,
                    isActive: domain.isActive ?? true
                })), { transaction });
            }



            const domainPoolWithAssociations = await StaticDomainPoolModel.findByPk(staticDomainPoolId, {
                include: domainPoolAssociations,
                transaction
            });

            return toInfo(domainPoolWithAssociations);
        });
    }

    public async update(staticDomainPoolId: number,
                        data: Partial<StaticDomainPoolAttributes>): Promise<StaticDomainPoolAttributes> {

        return db.transaction(async transaction => {
            const domainPool = await StaticDomainPoolModel.findByPk(staticDomainPoolId, { transaction });
            if (!domainPool) {
                throw new Errors.DomainPoolNotFoundError();
            }

            if (data?.name) {
                await domainPool.update({ name: data.name }, { transaction });
            }

            if (data?.domains) {
                await validateStaticDomainsExist(data.domains, transaction);

                await StaticDomainPoolItemModel.destroy({ where: { staticDomainPoolId }, transaction });
                await StaticDomainPoolItemModel.bulkCreate(data.domains.map(domain => ({
                    staticDomainPoolId,
                    staticDomainId: domain.id,
                    isActive: domain.isActive ?? true
                })), { transaction });
            }



            const domainPoolWithAssociations = await StaticDomainPoolModel.findByPk(staticDomainPoolId, {
                include: domainPoolAssociations,
                transaction
            });

            const info = toInfo(domainPoolWithAssociations);
            StaticDomainPoolCache.reset(staticDomainPoolId);

            return info;
        });
    }

    public async findById(id: number): Promise<StaticDomainPoolAttributes> {
        const domainPool = await StaticDomainPoolCache.findOne(id);
        if (!domainPool) {
            throw new Errors.DomainPoolNotFoundError();
        }

        return toInfo(domainPool);
    }

    public async findAll(findOptions?: FindOptions<unknown>): Promise<StaticDomainPoolAttributes[]> {
        const domainPools = await StaticDomainPoolModel.findAll({
            ...findOptions,
            include: domainPoolAssociations
        });
        return domainPools.map(toInfo);
    }

    public async remove(id: number): Promise<void> {
        const domainPool = await StaticDomainPoolModel.findByPk(id);
        if (!domainPool) {
            throw new Errors.DomainPoolNotFoundError();
        }
        await StaticDomainPoolModel.destroy({ where: { id } });
        StaticDomainPoolCache.reset(id);
    }

    public async addDomain(staticDomainPoolId: number,
                           staticDomainId: number): Promise<StaticDomainPoolItemAttributes> {
        const staticDomain = await StaticDomainModel.findByPk(staticDomainId);
        if (!staticDomain) {
            throw new Errors.DomainNotFoundError(staticDomainId);
        }

        let item = await StaticDomainPoolItemModel.findOne({
            where: {
                staticDomainId,
                staticDomainPoolId
            }
        });
        if (item) {
            throw new Errors.DomainPoolItemAlreadyExistsError();
        }

        item = await StaticDomainPoolItemModel.create({
            staticDomainId,
            staticDomainPoolId
        });

        const result = item.toJSON();
        StaticDomainPoolCache.reset(staticDomainPoolId);

        return result;
    }

    public async removeDomain(staticDomainPoolId: number, staticDomainId: number): Promise<void> {
        const existing = await StaticDomainPoolItemModel.findOne({
            where: {
                staticDomainId,
                staticDomainPoolId
            }
        });
        if (!existing) {
            throw new Errors.DomainPoolItemNotFoundError();
        }

        await StaticDomainPoolItemModel.destroy({
            where: {
                staticDomainId,
                staticDomainPoolId
            }
        });
        StaticDomainPoolCache.reset(staticDomainPoolId);
    }

    public disableDomain(poolId: number, domainId: number): Promise<void> {
        return this.updatePoolItemStatus(poolId, domainId, false);
    }

    public enableDomain(poolId: number, domainId: number): Promise<void> {
        return this.updatePoolItemStatus(poolId, domainId, true);
    }



    private async updatePoolItemStatus(staticDomainPoolId: number, staticDomainId: number, isActive: boolean) {
        const staticDomain = await StaticDomainModel.findByPk(staticDomainId);
        if (!staticDomain) {
            throw new Errors.DomainNotFoundError(staticDomainId);
        }

        const existing = await StaticDomainPoolItemModel.findOne({
            where: {
                staticDomainId,
                staticDomainPoolId
            }
        });
        if (!existing) {
            throw new Errors.DomainPoolItemNotFoundError();
        }

        await StaticDomainPoolItemModel.update(
            { isActive },
            {
                where: {
                    staticDomainId,
                    staticDomainPoolId
                }
            }
        );
        StaticDomainPoolCache.reset(staticDomainPoolId);
    }


}

const staticDomainPoolService = lazy<StaticDomainPoolService>(() => new StaticDomainPoolServiceImpl());

export const getStaticDomainPoolService = () => staticDomainPoolService.get();
