# Domain Tags Implementation Guide

## 1. What are Domain Tags and Their Purpose

Domain tags are a security and access control mechanism in the Skywind Management API system that allows entities (brands, merchants, etc.) to restrict which static domains they can be assigned to. They serve as a whitelist filter that ensures entities can only use domains that match their configured domain tags.

### Key Purposes:
- **Access Control**: Restrict which static domains an entity can use
- **Security**: Prevent unauthorized domain assignments
- **Multi-tenancy**: Allow different entities to have access to different sets of domains
- **Domain Validation**: Ensure domain assignments comply with business rules

## 2. Domain Tags Definition and Storage

### Database Schema
Domain tags are stored in the `entities` table as a JSONB column:

```sql
staticDomainTags: {
    type: DataTypes.JSONB,
    allowNull: true,
    field: "static_domain_tags"
}
```

### Data Structure
- **Type**: Array of strings (`string[]`)
- **Storage**: JSONB column in PostgreSQL
- **Nullable**: Yes (null means no restrictions)
- **Case Sensitivity**: Case-insensitive matching

### Example Data:
```json
["skywind.com", "gaming.example.com", "casino.test.com"]
```

## 3. Key Classes and Components

### Core Interfaces and Services

#### DomainTagsService Interface
```typescript
export interface DomainTagsService {
    setTags(entity: BaseEntity, tags: string[]): Promise<EntityInfo>;
    resetTags(entity: BaseEntity): Promise<EntityInfo>;
}
```

#### StaticEntityDomainService
- **Location**: `packages/api/src/skywind/services/entityDomainService.ts`
- **Purpose**: Manages static domain assignments and domain tags
- **Key Methods**:
  - `setTags()`: Sets domain tags for an entity
  - `resetTags()`: Removes all domain tags from an entity
  - `set()`: Assigns a static domain (validates against tags)

#### DynamicEntityDomainService
- **Note**: Domain tags are **NOT supported** for dynamic domains
- **Error**: Throws `DynamicDomainTagsNotSupportedError` when attempting to use tags

### Entity Models

#### BaseEntity Interface
```typescript
export interface BaseEntity {
    staticDomainTags?: string[];
    // ... other properties
}
```

#### EntityModel (Sequelize)
- **Location**: `packages/api/src/skywind/models/entity.ts`
- **Field**: `staticDomainTags` (maps to `static_domain_tags` column)

## 4. API Endpoints

### Set Domain Tags
```
PUT /:path/entitydomain/static/tags
```
- **Authentication**: Required
- **Authorization**: Required
- **Validation**: `isDomainArray` - validates array of domain strings
- **Handler**: `setStaticDomainTagsHandler`

### Reset Domain Tags
```
DELETE /:path/entitydomain/static/tags
```
- **Authentication**: Required
- **Authorization**: Required
- **Handler**: `resetStaticDomainTagsHandler`

### Request/Response Format
```typescript
// Request body for setting tags
{
    "tags": ["domain1.com", "domain2.com"]
}

// Response (EntityInfo object)
{
    "id": "entityId",
    "staticDomainTags": ["domain1.com", "domain2.com"],
    // ... other entity properties
}
```

## 5. Business Logic and Validation

### Domain Validation Logic
Located in `BaseEntity.validateStaticDomain()`:

```typescript
public validateStaticDomain(domain: string): boolean {
    if (!this.staticDomainTags) {
        return true; // No restrictions if no tags set
    }
    const cleanedDomain = domain.toLowerCase();
    for (const domainTag of this.staticDomainTags) {
        if (cleanedDomain.includes(domainTag.toLowerCase())) {
            return true; // Domain contains one of the allowed tags
        }
    }
    return false; // Domain doesn't match any tags
}
```

### Validation Rules:
1. **No Tags = No Restrictions**: If `staticDomainTags` is null/undefined, any domain is allowed
2. **Case-Insensitive Matching**: Domain comparison is case-insensitive
3. **Substring Matching**: Uses `includes()` - domain tag can be a substring of the domain
4. **Array Validation**: Tags must be a valid array of domain strings

### Static Domain Assignment Validation
When assigning a static domain to an entity:

```typescript
export async function validateStaticDomain(entity: BaseEntity, domain: StaticDomain) {
    if (!entity.validateStaticDomain(domain.domain)) {
        throw new Errors.ValidationError(`Domain is not valid. Allowed tags - ${entity.staticDomainTags}`);
    }
    // Additional parent entity restrictions...
}
```

## 6. Relationship with Other Entities

### Entity Hierarchy
- **Master Entities**: Can have domain tags set directly
- **Child Entities**: Inherit domain restrictions from parent entities
- **Domain Inheritance**: Child entities without static domains inherit from parents

### Static Domains
- **Domain Pool**: Entities can be assigned to static domain pools
- **Domain Assignment**: Must pass domain tag validation before assignment
- **Domain Types**: Supports STATIC, LOBBY, LIVE_STREAMING, EHUB types

### Caching
- **EntityCache**: Reset when domain tags are modified
- **StaticDomainCache**: Used for domain lookups during validation

## 7. Error Handling

### Custom Errors
```typescript
// Dynamic domain tags not supported
export class DynamicDomainTagsNotSupportedError extends SWError {
    constructor() {
        super(400, 791, "Dynamic domain tags not supported");
    }
}

// Validation error when domain doesn't match tags
throw new Errors.ValidationError(`Domain is not valid. Allowed tags - ${entity.staticDomainTags}`);
```

## 8. Usage Examples

### Setting Domain Tags via API
```bash
# Set domain tags for an entity
curl -X PUT /api/v1/entities/brand123/entitydomain/static/tags \
  -H "Content-Type: application/json" \
  -d '{"tags": ["skywind.com", "gaming.example.com"]}'

# Reset domain tags
curl -X DELETE /api/v1/entities/brand123/entitydomain/static/tags
```

### Programmatic Usage
```typescript
// Set domain tags
const service = getStaticEntityDomainService();
const result = await service.setTags(entity, ["skywind.com", "test.com"]);

// Reset domain tags
const result = await service.resetTags(entity);

// Validate domain assignment
const domain = await staticDomainService.findOne(domainId);
await validateStaticDomain(entity, domain); // Throws error if invalid
```

## 9. Configuration and Setup

### Database Setup
- Uses Sequelize ORM with PostgreSQL
- JSONB column type for efficient JSON operations
- No additional indexes required (uses entity primary key)

### Permissions
- Requires `entitydomain:static:tags` permission group
- Specific permissions:
  - `entitydomain:static:tags:set`
  - `entitydomain:static:tags:reset`

### Validation Middleware
- Uses `isDomainArray` validator
- Validates each tag as a proper domain string
- Ensures non-empty array when setting tags

## 10. Testing

### Unit Tests
Located in `packages/api/src/test/entities/domain.spec.ts`:

```typescript
it("Set static domain should be rejected if not belongs to static domain tags", async () => {
    const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN);
    const staticDomainTags = ["chebureck-egor.com"];
    const brandWithTags = await factory.create(FACTORY.BRAND, {}, { staticDomainTags });

    await getStaticEntityDomainService().set(brandWithTags, staticDomain.id)
        .should.eventually.rejectedWith(Errors.ValidationError);
});
```

### Test Coverage
- Domain tag setting and resetting
- Domain validation with and without tags
- Error scenarios for invalid domains
- Dynamic domain tag rejection

This implementation provides a robust domain access control system that ensures entities can only be assigned to domains that match their configured domain tags, enhancing security and multi-tenancy support in the Skywind Management API.
